TASK-1: in chest - we spawn for example 50 gold and 5 resourdces - well can we spawn 50 coins (or resource) in a way that we don't instantiate 50 coins (DroppedResource) but only 1 DroppedResource item with amount - 50 coins? So that we dont have 50 dropped resources that then merge into 1 but we have 1 dropped resource with amount 50. Same if we spawn more than 1 resource type.

TASK-2: goblin that we have in region 5 should spawn coins not gold ore - adjust.

TASK-3: look at tree (tscn, cs), when player is behind it - it is semi transparent. Now, we have tree2 - it should work same way to be semi transparent when player is behind it.

TASK-4: chest - we have stone chest and player needs stone key. when i click e to open, but i dont have key - player speaks that he needs wooden key. fix it so that he speaks (and require by chest!) that he needs stone key when its stone chest.