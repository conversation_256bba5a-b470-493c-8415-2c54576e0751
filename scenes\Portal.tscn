[gd_scene load_steps=4 format=3 uid="uid://bmvnt1kexku0y"]

[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="1_fw72y"]
[ext_resource type="Script" uid="uid://uiu2cdwj5jb2" path="res://scenes/Portal.cs" id="1_portal_script"]

[sub_resource type="CircleShape2D" id="CircleShape2D_xy1rl"]

[node name="Portal" type="Node2D"]
script = ExtResource("1_portal_script")

[node name="PlayerDetector" type="Area2D" parent="."]
collision_mask = 4

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
scale = Vector2(1, 0.38)
shape = SubResource("CircleShape2D_xy1rl")

[node name="CanvasLayer" type="CanvasLayer" parent="."]
visible = false

[node name="Control" type="Control" parent="CanvasLayer"]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Sprite2D" type="Sprite2D" parent="CanvasLayer/Control"]
modulate = Color(0, 0, 0, 1)
self_modulate = Color(0, 0, 0, 1)
position = Vector2(3.75, 2.25)
scale = Vector2(8.5, 5.5)

[node name="LoadingText" parent="CanvasLayer/Control" instance=ExtResource("1_fw72y")]
layout_mode = 0
offset_left = -84.0
offset_top = 2.0
offset_right = 84.0
offset_bottom = 26.0
text = "LOADING_TEXT"
