[gd_scene load_steps=12 format=3 uid="uid://ocpnbejvu51t"]

[ext_resource type="Texture2D" uid="uid://bndjqyjofeei7" path="res://resources/exterior/Graveyard_Candle_Off2.png" id="1_3ayf4"]
[ext_resource type="Script" uid="uid://cndsdyj8dyleq" path="res://scenes/regions/region6/GraveyardCandle.cs" id="1_script"]
[ext_resource type="Script" uid="uid://yy26ktmhridb" path="res://scenes/regions/region6/CandleLight.cs" id="2_candlelight"]
[ext_resource type="Texture2D" uid="uid://cqjs7g0rnsfhf" path="res://resources/exterior/Graveyard_Candle_Standing_16x16_2.png" id="2_iwcyb"]

[sub_resource type="Animation" id="Animation_saf3j"]
resource_name = "Off"
length = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Off:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Animating:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_80t8h"]
resource_name = "On"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Animating:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Off:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Animating:frame")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3]
}

[sub_resource type="Animation" id="Animation_uubtr"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Off:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Animating:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Animating:frame")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_3ayf4"]
_data = {
&"Off": SubResource("Animation_saf3j"),
&"On": SubResource("Animation_80t8h"),
&"RESET": SubResource("Animation_uubtr")
}

[sub_resource type="CircleShape2D" id="CircleShape2D_3ayf4"]
radius = 12.0

[sub_resource type="Gradient" id="Gradient_light"]
offsets = PackedFloat32Array(0, 0.769634)
colors = PackedColorArray(1, 1, 1, 1, 1, 1, 1, 0)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_candle"]
gradient = SubResource("Gradient_light")
width = 32
height = 32
fill = 1
fill_from = Vector2(0.5, 0.5)
metadata/_snap_enabled = true

[node name="GraveyardCandle" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_script")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_3ayf4")
}
speed_scale = 0.7

[node name="Off" type="Sprite2D" parent="."]
y_sort_enabled = true
texture = ExtResource("1_3ayf4")

[node name="Animating" type="Sprite2D" parent="."]
visible = false
y_sort_enabled = true
texture = ExtResource("2_iwcyb")
hframes = 4

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(-7, 12, -4, 14, 4, 14, 6, 12, 6, 7, 4, 5, -4, 5, -7, 7)

[node name="PlayerDetector" type="Area2D" parent="."]
collision_mask = 4

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
position = Vector2(0, 6)
shape = SubResource("CircleShape2D_3ayf4")

[node name="CandleLight" type="PointLight2D" parent="."]
position = Vector2(0, 2)
color = Color(1, 0.9, 0.7, 1)
energy = 0.8
texture = SubResource("GradientTexture2D_candle")
texture_scale = 1.5

[node name="CandleLightScript" type="Node2D" parent="."]
script = ExtResource("2_candlelight")
LightRadius = 112.0
